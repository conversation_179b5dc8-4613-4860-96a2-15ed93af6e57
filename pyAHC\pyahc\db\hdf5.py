# ruff: noqa: S301, TRY300
# mypy: disable-error-code="operator, index"
# operator 和 index 错误出现在 _load_pickled 方法中。
# 如果需要，将来应观察并修复此问题。

# 也许在下一个版本中，我们可以实现更安全地处理 pickle 的方法。
# 目前，这不是一个高优先级。
# 错误 TRY300 在这种情况下也显得过于严格。
"""与 HDF5 文件交互。

HDF5 数据库旨在以结构化方式存储 pyAHC 模型和结果。
该类允许用户将多个模型和结果保存和加载到单个 .h5 文件中。

类：
    HDF5: 用于与 HDF5 文件交互的类。
"""

import logging
import pickle
from typing import TYPE_CHECKING, Literal, Union

import h5py
import numpy as np
from pydantic import BaseModel, Field, computed_field

if TYPE_CHECKING:
    from pyahc.model.model import Model
    from pyahc.model.result import Result

logger = logging.getLogger(__name__)


class HDF5(BaseModel):
    """用于与 HDF5 文件交互的类。

    pyAHC 模型和结果以 pickled 对象的形式存储在 HDF5 文件中。
    这些是模型和结果的完整表示，可以加载回内存。
    它们还附带一些元数据，这些元数据作为属性存储在 HDF5 文件中。
    用户可以在 HDF5 文件查看器中查看模型。

    属性：
        filename (str): HDF5 文件的路径。
        models (dict): 包含已加载模型的字典。
    """

    filename: str
    models: dict | None = Field(default_factory=dict)

    @computed_field(return_type=dict)
    def list_projects(self):
        """列出 HDF5 文件中的所有项目。"""
        with h5py.File(self.filename, "r") as f:
            projects = list(f.keys())
        return projects

    @computed_field(return_type=dict)
    def list_models(self):
        """列出 HDF5 文件中的所有模型。"""
        with h5py.File(self.filename, "r") as f:
            # Use the visititems method to traverse the file structure
            models = {project: list(f[project].keys()) for project in f}
        return models

    @staticmethod
    def _get_or_create_group(f, group_name):
        """Get a group from an HDF5 file or create it if it does not exist."""
        return f.require_group(group_name)

    def save_model(
        self,
        model: "Model",
        result: Union["Result", None] = None,
        overwrite_datasets: bool = False,
        overwrite_project: bool = False,
        mode: Literal["python", "json", "yaml", "plain"] = "python",
    ):
        """
        将模型及其结果保存到 HDF5 文件。

        参数：
            model (Model): 要保存的模型。
            result (Result, optional): 要保存的结果。
            overwrite_datasets (bool): 如果为 True，则覆盖现有数据集。
            overwrite_project (bool): 如果为 True，则覆盖现有项目。
            mode (Literal["python", "json", "yaml"]): 保存数据的格式。
                目前仅支持“python”。

        引发：
            NotImplementedError: 如果选择了“json”或“yaml”模式。
        """
        with h5py.File(self.filename, "a") as f:
            # Handle project overwriting
            if overwrite_project:
                self._overwrite_project(f, model.metadata.project)

            # Create or retrieve project group
            project_group = self._get_or_create_group(f, model.metadata.project)
            self._update_attributes(project_group, model.metadata.__dict__)

            # Create or retrieve model group
            model_group = self._get_or_create_group(project_group, model.version)

            # Handle dataset overwriting
            if overwrite_datasets:
                self._overwrite_datasets(model_group)

            # Save data based on the mode
            if mode == "python":
                self._save_pickled(model_group, "input", model)
                if result:
                    self._save_pickled(model_group, "output", result)
            else:
                raise NotImplementedError(f"Mode '{mode}' is not yet implemented.")

    def load(
        self,
        project: str,
        model: str | None = None,
        load_results: bool = False,
        mode: Literal["python", "json", "yaml"] = "python",
    ) -> dict[str, tuple["Model", Union["Result", None]]]:
        """
        加载单个模型或特定项目中的所有模型。

        参数：
            project (str): 项目名称。
            model (str, optional): 模型名称。如果为 None，则加载项目中的所有模型。
            load_results (bool): 是否随模型一起加载结果。
            mode (Literal["python", "json", "yaml"]): 加载数据的格式。
                目前仅支持“python”。

        返回：
            dict[str, tuple[Model, Union[Result, None]]]: 已加载模型及其结果的字典。

        引发：
            NotImplementedError: 如果选择了“json”或“yaml”模式。
        """
        loaded_models = {}

        with h5py.File(self.filename, "r") as f:
            # Validate that the project exists
            if project not in f:
                msg = f"Project '{project}' does not exist in the HDF5 file."
                raise KeyError(msg)

            # Get all models in the project
            all_models = self.list_models[project]

            if mode == "python":
                # Load all models if no specific model is provided
                if model is None:
                    for item in all_models:
                        loaded_models[item] = self._load_pickled(
                            group=f[project], name=item, load_results=load_results
                        )
                else:
                    # Validate that the specific model exists
                    if model not in all_models:
                        msg = f"Model '{model}' does not exist in project '{project}'."
                        raise KeyError(msg)
                    loaded_models[model] = self._load_pickled(
                        group=f[project], name=model, load_results=load_results
                    )
            else:
                msg = f"Mode '{mode}' is not yet implemented."
                raise NotImplementedError(msg)

        # Update internal models dictionary and return loaded models
        self.models.update(loaded_models)
        return loaded_models

    def delete(self, project: str, model: str | None = None):
        """删除单个模型或特定项目中的所有模型。

        !!! 警告

            此方法仅用于小规模删除，因为目前它不执行 HDF5 文件的重新打包
            （对象被删除但磁盘空间未释放）。对于大规模删除，请考虑创建新的 HDF5 文件并仅保存您想保留的模型。

        参数：
            project (str): 项目名称。
            model (str): 模型名称。
        """
        with h5py.File(self.filename, "a") as f:
            if model is None:
                try:
                    del f[project]
                except KeyError:
                    logger.warning(f"Project {project} does not exist.")
            else:
                try:
                    del f[project][model]
                except KeyError:
                    logger.warning(
                        f"Model {model} does not exist in project {project}."
                    )

    def _overwrite_datasets(self, group):
        """删除组中的所有数据集。"""
        for key in list(group.keys()):
            try:
                del group[key]
                logger.info(f"Deleted dataset {key} in group {group.name}")
            except KeyError:
                logger.warning(f"Failed to delete dataset {key} in group {group.name}")

    def _overwrite_project(self, file, project_name):
        """从 HDF5 文件中删除项目组。"""
        try:
            del file[project_name]
            logger.info(f"Deleted project {project_name}")
        except KeyError:
            logger.warning(f"Project {project_name} does not exist.")

    def _save_plain_string(self, group, name, data):
        """将数据保存为纯字符串数据集。"""

    def _save_pickled(self, group, name, data):
        """将数据保存为 pickled 数据集。"""
        try:
            pickle_data = pickle.dumps(data)
            group.create_dataset(name, data=np.void(pickle_data))
            logger.info(f"Saved {name} to {group.name}")
        except ValueError as e:
            logger.warning(
                f"Failed to create dataset {name} in {group.name}. Error: {e}"
            )

    def _update_attributes(self, group, attributes):
        """更新 HDF5 组的属性。"""
        sanitized_attrs = {k: v for k, v in attributes.items() if v is not None}
        group.attrs.update(sanitized_attrs)

    def _load_pickled(
        self, group: h5py.Group, name: str, load_results: bool
    ) -> tuple["Model", Union["Result", None]]:
        """
        从 HDF5 组加载 pickled 模型及其可选结果。

        参数：
            group (h5py.Group): 包含模型数据的 HDF5 组。
            name (str): 要加载的模型名称。
            load_results (bool): 是否随模型一起加载结果。

        返回：
            tuple[Model, Union[Result, None]]: 已加载的模型及其结果（如果适用）。
        """
        try:
            # Load pickled input data
            pickle_in = group[name]["input"][()].tobytes()
            model: Model = pickle.loads(pickle_in)

            # Optionally load pickled output data
            result: Result = None

            if load_results and "output" in group[name]:
                pickle_out = group[name]["output"][()].tobytes()
                result = pickle.loads(pickle_out)

            logger.info(f"Loaded model '{name}' from group '{group.name}'.")
            return model, result
        except Exception:
            logger.exception(f"Failed to load model '{name}' from group '{group.name}'")
            return None, None
